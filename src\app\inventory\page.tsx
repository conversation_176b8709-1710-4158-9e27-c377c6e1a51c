'use client'

import React, { useState } from 'react'
import AppLayout from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Modal from '@/components/ui/Modal'
import { Plus, Search, Edit, Trash2, Package } from 'lucide-react'
import { ItemStatus } from '@/types'

// Mock data untuk sementara
const mockItems = [
  {
    id: '1',
    name: 'Laptop Dell Inspiron',
    description: 'Laptop untuk keperluan kantor',
    stock: 5,
    minStock: 2,
    status: ItemStatus.AVAILABLE,
    category: { name: 'Elektronik' },
    location: { name: 'Rak A-1' },
    createdAt: new Date('2025-01-15'),
    updatedAt: new Date('2025-01-28')
  },
  {
    id: '2',
    name: 'Mouse Wireless',
    description: 'Mouse wireless untuk komputer',
    stock: 1,
    minStock: 5,
    status: ItemStatus.AVAILABLE,
    category: { name: '<PERSON><PERSON><PERSON><PERSON>' },
    location: { name: 'Rak B-2' },
    createdAt: new Date('2025-01-20'),
    updatedAt: new Date('2025-01-27')
  },
  {
    id: '3',
    name: 'Proyektor Epson',
    description: 'Proyektor untuk presentasi',
    stock: 0,
    minStock: 1,
    status: ItemStatus.OUT_OF_STOCK,
    category: { name: 'Elektronik' },
    location: { name: 'Rak C-1' },
    createdAt: new Date('2025-01-10'),
    updatedAt: new Date('2025-01-25')
  }
]

const mockCategories = [
  { id: '1', name: 'Elektronik' },
  { id: '2', name: 'Aksesoris' },
  { id: '3', name: 'Furniture' }
]

const mockLocations = [
  { id: '1', name: 'Rak A-1' },
  { id: '2', name: 'Rak B-2' },
  { id: '3', name: 'Rak C-1' }
]

const getStatusColor = (status: ItemStatus) => {
  switch (status) {
    case ItemStatus.AVAILABLE:
      return 'text-green-600 bg-green-100'
    case ItemStatus.OUT_OF_STOCK:
      return 'text-red-600 bg-red-100'
    case ItemStatus.DISCONTINUED:
      return 'text-gray-600 bg-gray-100'
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

const getStockColor = (stock: number, minStock: number) => {
  if (stock === 0) return 'text-red-600'
  if (stock <= minStock) return 'text-yellow-600'
  return 'text-green-600'
}

const InventoryPage: React.FC = () => {
  const [items, setItems] = useState(mockItems)
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<any>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    stock: 0,
    minStock: 5,
    categoryId: '',
    locationId: ''
  })

  const filteredItems = items.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.location.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAddItem = () => {
    setEditingItem(null)
    setFormData({
      name: '',
      description: '',
      stock: 0,
      minStock: 5,
      categoryId: '',
      locationId: ''
    })
    setIsModalOpen(true)
  }

  const handleEditItem = (item: any) => {
    setEditingItem(item)
    setFormData({
      name: item.name,
      description: item.description || '',
      stock: item.stock,
      minStock: item.minStock,
      categoryId: '1', // Mock category ID
      locationId: '1'  // Mock location ID
    })
    setIsModalOpen(true)
  }

  const handleDeleteItem = (itemId: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus item ini?')) {
      setItems(items.filter(item => item.id !== itemId))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (editingItem) {
      // Update existing item
      setItems(items.map(item => 
        item.id === editingItem.id 
          ? {
              ...item,
              ...formData,
              status: formData.stock > 0 ? ItemStatus.AVAILABLE : ItemStatus.OUT_OF_STOCK,
              updatedAt: new Date()
            }
          : item
      ))
    } else {
      // Add new item
      const newItem = {
        id: Date.now().toString(),
        ...formData,
        status: formData.stock > 0 ? ItemStatus.AVAILABLE : ItemStatus.OUT_OF_STOCK,
        category: mockCategories.find(c => c.id === formData.categoryId) || mockCategories[0],
        location: mockLocations.find(l => l.id === formData.locationId) || mockLocations[0],
        createdAt: new Date(),
        updatedAt: new Date()
      }
      setItems([...items, newItem])
    }
    
    setIsModalOpen(false)
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Manajemen Barang</h1>
            <p className="text-gray-600 mt-1">
              Kelola inventaris barang di gudang
            </p>
          </div>
          <Button onClick={handleAddItem} className="flex items-center space-x-2">
            <Plus className="h-4 w-4" />
            <span>Tambah Barang</span>
          </Button>
        </div>

        {/* Search and Filters */}
        <Card className="glass">
          <CardContent className="p-4">
            <div className="flex space-x-4">
              <div className="flex-1">
                <Input
                  placeholder="Cari barang, kategori, atau lokasi..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  leftIcon={<Search className="h-4 w-4" />}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Items Table */}
        <Card className="glass">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">
              Daftar Barang ({filteredItems.length})
            </h3>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Nama Barang</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Kategori</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Lokasi</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Stok</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredItems.map((item) => (
                    <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-gray-900">{item.name}</div>
                          {item.description && (
                            <div className="text-sm text-gray-500">{item.description}</div>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-700">{item.category.name}</td>
                      <td className="py-3 px-4 text-gray-700">{item.location.name}</td>
                      <td className="py-3 px-4">
                        <span className={`font-medium ${getStockColor(item.stock, item.minStock)}`}>
                          {item.stock}
                        </span>
                        <span className="text-gray-500 text-sm"> / min {item.minStock}</span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                          {item.status === ItemStatus.AVAILABLE ? 'Tersedia' : 
                           item.status === ItemStatus.OUT_OF_STOCK ? 'Habis' : 'Dihentikan'}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditItem(item)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteItem(item.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Add/Edit Modal */}
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title={editingItem ? 'Edit Barang' : 'Tambah Barang Baru'}
          size="md"
        >
          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              label="Nama Barang"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
            <Input
              label="Deskripsi"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            />
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Stok"
                type="number"
                value={formData.stock}
                onChange={(e) => setFormData({ ...formData, stock: parseInt(e.target.value) || 0 })}
                required
              />
              <Input
                label="Minimum Stok"
                type="number"
                value={formData.minStock}
                onChange={(e) => setFormData({ ...formData, minStock: parseInt(e.target.value) || 0 })}
                required
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Kategori
                </label>
                <select
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  value={formData.categoryId}
                  onChange={(e) => setFormData({ ...formData, categoryId: e.target.value })}
                  required
                >
                  <option value="">Pilih Kategori</option>
                  {mockCategories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Lokasi
                </label>
                <select
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  value={formData.locationId}
                  onChange={(e) => setFormData({ ...formData, locationId: e.target.value })}
                  required
                >
                  <option value="">Pilih Lokasi</option>
                  {mockLocations.map((location) => (
                    <option key={location.id} value={location.id}>
                      {location.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsModalOpen(false)}
              >
                Batal
              </Button>
              <Button type="submit">
                {editingItem ? 'Update' : 'Tambah'} Barang
              </Button>
            </div>
          </form>
        </Modal>
      </div>
    </AppLayout>
  )
}

export default InventoryPage
