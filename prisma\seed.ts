import { PrismaClient } from '@prisma/client'
import { ItemStatus, BorrowingStatus, ActivityType } from '../src/types'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Clear existing data
  await prisma.activity.deleteMany()
  await prisma.borrowing.deleteMany()
  await prisma.item.deleteMany()
  await prisma.category.deleteMany()
  await prisma.location.deleteMany()

  console.log('🗑️  Cleared existing data')

  // Create categories
  const categories = await Promise.all([
    prisma.category.create({
      data: {
        name: 'Elektronik',
        description: 'Perangkat elektronik dan teknologi'
      }
    }),
    prisma.category.create({
      data: {
        name: 'Aks<PERSON><PERSON>',
        description: 'Aks<PERSON>oris komputer dan perangkat'
      }
    }),
    prisma.category.create({
      data: {
        name: 'Furniture',
        description: 'Perabotan kantor dan meja'
      }
    }),
    prisma.category.create({
      data: {
        name: '<PERSON><PERSON>',
        description: '<PERSON><PERSON>g<PERSON>pan tulis dan kantor'
      }
    }),
    prisma.category.create({
      data: {
        name: '<PERSON><PERSON><PERSON>',
        description: '<PERSON>alatan dan tools lainnya'
      }
    })
  ])

  console.log('📂 Created categories')

  // Create locations
  const locations = await Promise.all([
    prisma.location.create({
      data: {
        name: 'Rak A-1',
        description: 'Rak bagian A lantai 1'
      }
    }),
    prisma.location.create({
      data: {
        name: 'Rak A-2',
        description: 'Rak bagian A lantai 2'
      }
    }),
    prisma.location.create({
      data: {
        name: 'Rak B-1',
        description: 'Rak bagian B lantai 1'
      }
    }),
    prisma.location.create({
      data: {
        name: 'Rak B-2',
        description: 'Rak bagian B lantai 2'
      }
    }),
    prisma.location.create({
      data: {
        name: 'Rak C-1',
        description: 'Rak bagian C lantai 1'
      }
    }),
    prisma.location.create({
      data: {
        name: 'Gudang Utama',
        description: 'Gudang penyimpanan utama'
      }
    })
  ])

  console.log('📍 Created locations')

  // Create items
  const items = await Promise.all([
    // Elektronik
    prisma.item.create({
      data: {
        name: 'Laptop Dell Inspiron 15',
        description: 'Laptop untuk keperluan kantor dengan spesifikasi standar',
        stock: 8,
        minStock: 2,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[0].id,
        locationId: locations[0].id
      }
    }),
    prisma.item.create({
      data: {
        name: 'Laptop HP Pavilion',
        description: 'Laptop HP untuk presentasi dan meeting',
        stock: 5,
        minStock: 1,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[0].id,
        locationId: locations[0].id
      }
    }),
    prisma.item.create({
      data: {
        name: 'Proyektor Epson EB-X41',
        description: 'Proyektor untuk presentasi dan meeting',
        stock: 3,
        minStock: 1,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[0].id,
        locationId: locations[1].id
      }
    }),
    prisma.item.create({
      data: {
        name: 'Monitor LG 24 inch',
        description: 'Monitor tambahan untuk workstation',
        stock: 12,
        minStock: 3,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[0].id,
        locationId: locations[1].id
      }
    }),
    prisma.item.create({
      data: {
        name: 'Printer Canon PIXMA',
        description: 'Printer untuk keperluan dokumen',
        stock: 0,
        minStock: 1,
        status: ItemStatus.OUT_OF_STOCK,
        categoryId: categories[0].id,
        locationId: locations[2].id
      }
    }),

    // Aksesoris
    prisma.item.create({
      data: {
        name: 'Mouse Wireless Logitech',
        description: 'Mouse wireless untuk komputer',
        stock: 25,
        minStock: 10,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[1].id,
        locationId: locations[2].id
      }
    }),
    prisma.item.create({
      data: {
        name: 'Keyboard Mechanical',
        description: 'Keyboard mechanical untuk typing',
        stock: 15,
        minStock: 5,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[1].id,
        locationId: locations[2].id
      }
    }),
    prisma.item.create({
      data: {
        name: 'Kabel HDMI 2m',
        description: 'Kabel HDMI untuk koneksi display',
        stock: 8,
        minStock: 5,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[1].id,
        locationId: locations[3].id
      }
    }),
    prisma.item.create({
      data: {
        name: 'USB Hub 4 Port',
        description: 'USB Hub untuk ekspansi port',
        stock: 2,
        minStock: 3,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[1].id,
        locationId: locations[3].id
      }
    }),

    // Furniture
    prisma.item.create({
      data: {
        name: 'Meja Kerja Portable',
        description: 'Meja kerja yang bisa dipindah-pindah',
        stock: 6,
        minStock: 2,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[2].id,
        locationId: locations[4].id
      }
    }),
    prisma.item.create({
      data: {
        name: 'Kursi Kantor Ergonomis',
        description: 'Kursi kantor dengan sandaran yang nyaman',
        stock: 10,
        minStock: 3,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[2].id,
        locationId: locations[4].id
      }
    }),

    // Alat Tulis
    prisma.item.create({
      data: {
        name: 'Whiteboard Portable',
        description: 'Whiteboard kecil untuk meeting',
        stock: 4,
        minStock: 2,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[3].id,
        locationId: locations[5].id
      }
    }),
    prisma.item.create({
      data: {
        name: 'Spidol Whiteboard Set',
        description: 'Set spidol warna untuk whiteboard',
        stock: 20,
        minStock: 8,
        status: ItemStatus.AVAILABLE,
        categoryId: categories[3].id,
        locationId: locations[5].id
      }
    })
  ])

  console.log('📦 Created items')

  // Create some borrowings
  const borrowings = await Promise.all([
    prisma.borrowing.create({
      data: {
        itemId: items[0].id, // Laptop Dell
        borrowerName: 'John Doe',
        purpose: 'Presentasi klien di kantor cabang',
        quantity: 1,
        expectedReturnDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        status: BorrowingStatus.ACTIVE,
        notes: 'Untuk presentasi project baru'
      }
    }),
    prisma.borrowing.create({
      data: {
        itemId: items[2].id, // Proyektor
        borrowerName: 'Jane Smith',
        purpose: 'Meeting tim mingguan',
        quantity: 1,
        expectedReturnDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago (overdue)
        status: BorrowingStatus.OVERDUE,
        notes: 'Meeting rutin setiap minggu'
      }
    }),
    prisma.borrowing.create({
      data: {
        itemId: items[5].id, // Mouse Wireless
        borrowerName: 'Bob Johnson',
        purpose: 'Work from home setup',
        quantity: 2,
        borrowDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        expectedReturnDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        returnDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // returned 1 day ago
        status: BorrowingStatus.RETURNED,
        notes: 'Dikembalikan dalam kondisi baik'
      }
    })
  ])

  console.log('📋 Created borrowings')

  // Update item stocks based on active borrowings
  await prisma.item.update({
    where: { id: items[0].id },
    data: { stock: { decrement: 1 } } // Laptop Dell borrowed
  })

  await prisma.item.update({
    where: { id: items[2].id },
    data: { stock: { decrement: 1 } } // Proyektor borrowed
  })

  console.log('📊 Updated item stocks')

  // Create activities
  const activities = await Promise.all([
    prisma.activity.create({
      data: {
        type: ActivityType.ITEM_BORROWED,
        description: 'Laptop Dell Inspiron 15 dipinjam oleh John Doe',
        itemId: items[0].id,
        borrowingId: borrowings[0].id,
        metadata: {
          borrowerName: 'John Doe',
          purpose: 'Presentasi klien di kantor cabang',
          quantity: 1
        }
      }
    }),
    prisma.activity.create({
      data: {
        type: ActivityType.ITEM_BORROWED,
        description: 'Proyektor Epson EB-X41 dipinjam oleh Jane Smith',
        itemId: items[2].id,
        borrowingId: borrowings[1].id,
        metadata: {
          borrowerName: 'Jane Smith',
          purpose: 'Meeting tim mingguan',
          quantity: 1
        }
      }
    }),
    prisma.activity.create({
      data: {
        type: ActivityType.ITEM_RETURNED,
        description: '2 unit Mouse Wireless Logitech dikembalikan oleh Bob Johnson',
        itemId: items[5].id,
        borrowingId: borrowings[2].id,
        metadata: {
          borrowerName: 'Bob Johnson',
          quantity: 2,
          returnDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
        }
      }
    }),
    prisma.activity.create({
      data: {
        type: ActivityType.ITEM_ADDED,
        description: 'Monitor LG 24 inch ditambahkan ke inventaris',
        itemId: items[3].id,
        metadata: {
          initialStock: 12,
          category: 'Elektronik',
          location: 'Rak A-2'
        }
      }
    }),
    prisma.activity.create({
      data: {
        type: ActivityType.STOCK_UPDATED,
        description: 'Stok Spidol Whiteboard Set diperbarui menjadi 20 unit',
        itemId: items[12].id,
        metadata: {
          previousStock: 15,
          newStock: 20,
          reason: 'Pembelian tambahan'
        }
      }
    })
  ])

  console.log('📝 Created activities')

  console.log('✅ Database seeding completed successfully!')
  console.log(`
📊 Summary:
- Categories: ${categories.length}
- Locations: ${locations.length}
- Items: ${items.length}
- Borrowings: ${borrowings.length}
- Activities: ${activities.length}
  `)
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
