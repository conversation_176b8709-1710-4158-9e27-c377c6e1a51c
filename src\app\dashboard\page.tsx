'use client'

import React from 'react'
import AppLayout from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { Package, AlertTriangle, ArrowRightLeft, Clock } from 'lucide-react'

// Mock data untuk sementara
const mockKPI = {
  totalItems: 156,
  lowStockItems: 8,
  totalBorrowedItems: 23,
  overdueItems: 3
}

const mockRecentActivities = [
  {
    id: '1',
    type: 'ITEM_BORROWED',
    description: 'Laptop Dell dipinjam oleh John Doe',
    timestamp: new Date('2025-01-28T10:30:00'),
    itemName: 'Laptop Dell'
  },
  {
    id: '2',
    type: 'ITEM_RETURNED',
    description: 'Proyektor Epson dikembalikan oleh <PERSON>',
    timestamp: new Date('2025-01-28T09:15:00'),
    itemName: 'Proyektor Epson'
  },
  {
    id: '3',
    type: 'ITEM_ADDED',
    description: 'Mouse Wireless baru ditambahkan ke inventaris',
    timestamp: new Date('2025-01-28T08:45:00'),
    itemName: 'Mouse Wireless'
  },
  {
    id: '4',
    type: 'STOCK_UPDATED',
    description: 'Stok Kabel HDMI diperbarui menjadi 15 unit',
    timestamp: new Date('2025-01-27T16:20:00'),
    itemName: 'Kabel HDMI'
  }
]

const KPICard: React.FC<{
  title: string
  value: number
  icon: React.ReactNode
  color: string
  bgColor: string
}> = ({ title, value, icon, color, bgColor }) => {
  return (
    <Card className="glass hover:shadow-lg transition-all duration-200">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className={`text-3xl font-bold ${color}`}>{value}</p>
          </div>
          <div className={`p-3 rounded-full ${bgColor}`}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

const ActivityItem: React.FC<{
  activity: typeof mockRecentActivities[0]
}> = ({ activity }) => {
  const getActivityColor = (type: string) => {
    switch (type) {
      case 'ITEM_BORROWED':
        return 'text-blue-600 bg-blue-100'
      case 'ITEM_RETURNED':
        return 'text-green-600 bg-green-100'
      case 'ITEM_ADDED':
        return 'text-purple-600 bg-purple-100'
      case 'STOCK_UPDATED':
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
      <div className={`w-2 h-2 rounded-full mt-2 ${getActivityColor(activity.type).split(' ')[1]}`} />
      <div className="flex-1 min-w-0">
        <p className="text-sm text-gray-900">{activity.description}</p>
        <p className="text-xs text-gray-500">{formatTime(activity.timestamp)}</p>
      </div>
    </div>
  )
}

const DashboardPage: React.FC = () => {
  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Selamat datang di DisaTools - Sistem Manajemen Inventaris
          </p>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <KPICard
            title="Total Barang"
            value={mockKPI.totalItems}
            icon={<Package className="h-6 w-6 text-blue-600" />}
            color="text-blue-600"
            bgColor="bg-blue-100"
          />
          <KPICard
            title="Stok Rendah"
            value={mockKPI.lowStockItems}
            icon={<AlertTriangle className="h-6 w-6 text-yellow-600" />}
            color="text-yellow-600"
            bgColor="bg-yellow-100"
          />
          <KPICard
            title="Sedang Dipinjam"
            value={mockKPI.totalBorrowedItems}
            icon={<ArrowRightLeft className="h-6 w-6 text-green-600" />}
            color="text-green-600"
            bgColor="bg-green-100"
          />
          <KPICard
            title="Terlambat"
            value={mockKPI.overdueItems}
            icon={<Clock className="h-6 w-6 text-red-600" />}
            color="text-red-600"
            bgColor="bg-red-100"
          />
        </div>

        {/* Recent Activities */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="glass">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">
                Aktivitas Terbaru
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                {mockRecentActivities.map((activity) => (
                  <ActivityItem key={activity.id} activity={activity} />
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card className="glass">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">
                Ringkasan Cepat
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Kategori Terbanyak</span>
                  <span className="text-sm font-medium">Elektronik (45)</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Lokasi Terpadat</span>
                  <span className="text-sm font-medium">Rak A-1 (28)</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Peminjaman Hari Ini</span>
                  <span className="text-sm font-medium">7 item</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Pengembalian Hari Ini</span>
                  <span className="text-sm font-medium">4 item</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  )
}

export default DashboardPage
