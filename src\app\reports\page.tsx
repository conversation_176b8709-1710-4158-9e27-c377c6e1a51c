'use client'

import React, { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import AppLayout from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { FileText, Download, Calendar, Filter, AlertCircle } from 'lucide-react'
import { exportToPDF, exportToExcel, prepareBorrowingDataForExport } from '@/lib/exportUtils'

// Mock data untuk reports
const mockReportData = [
  {
    id: '1',
    borrowerName: '<PERSON>',
    itemName: 'Laptop Dell Inspiron',
    category: 'Elektronik',
    borrowDate: '2025-01-25',
    returnDate: '2025-01-28',
    purpose: 'Presentasi klien',
    status: 'Dikembalikan'
  },
  {
    id: '2',
    borrowerName: '<PERSON>',
    itemName: 'Proyektor Epson',
    category: 'Elektronik',
    borrowDate: '2025-01-20',
    returnDate: null,
    purpose: 'Meeting tim',
    status: 'Terlambat'
  },
  {
    id: '3',
    borrowerName: '<PERSON>',
    itemName: 'Mouse Wireless',
    category: 'Aksesoris',
    borrowDate: '2025-01-22',
    returnDate: '2025-01-28',
    purpose: 'Work from home',
    status: 'Dikembalikan'
  }
]

const ReportsPage: React.FC = () => {
  const searchParams = useSearchParams()
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    category: '',
    status: ''
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [exportMessage, setExportMessage] = useState<{ type: 'success' | 'error', message: string } | null>(null)

  // Pre-fill filters from URL params (from Calendar integration)
  useEffect(() => {
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const category = searchParams.get('category')
    const status = searchParams.get('status')

    if (dateFrom || dateTo || category || status) {
      setFilters({
        dateFrom: dateFrom || '',
        dateTo: dateTo || '',
        category: category || '',
        status: status || ''
      })
    }
  }, [searchParams])

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }))
  }

  const handleGenerateReport = async (format: 'pdf' | 'excel') => {
    setIsGenerating(true)
    setExportMessage(null)

    try {
      // Prepare export data
      const exportData = prepareBorrowingDataForExport(filteredData)

      let result
      if (format === 'pdf') {
        result = await exportToPDF(exportData, filters)
      } else {
        result = await exportToExcel(exportData, filters)
      }

      if (result.success) {
        setExportMessage({
          type: 'success',
          message: `Laporan ${format.toUpperCase()} berhasil diunduh: ${result.filename}`
        })
      } else {
        setExportMessage({
          type: 'error',
          message: result.error || `Gagal generate laporan ${format.toUpperCase()}`
        })
      }
    } catch (error) {
      console.error('Export error:', error)
      setExportMessage({
        type: 'error',
        message: `Terjadi kesalahan saat generate laporan ${format.toUpperCase()}`
      })
    } finally {
      setIsGenerating(false)

      // Clear message after 5 seconds
      setTimeout(() => {
        setExportMessage(null)
      }, 5000)
    }
  }

  const filteredData = mockReportData.filter(item => {
    const matchesCategory = !filters.category || item.category === filters.category
    const matchesStatus = !filters.status || item.status === filters.status
    const matchesDateFrom = !filters.dateFrom || new Date(item.borrowDate) >= new Date(filters.dateFrom)
    const matchesDateTo = !filters.dateTo || new Date(item.borrowDate) <= new Date(filters.dateTo)

    return matchesCategory && matchesStatus && matchesDateFrom && matchesDateTo
  })

  const summary = {
    totalBorrowings: filteredData.length,
    totalReturned: filteredData.filter(item => item.status === 'Dikembalikan').length,
    totalActive: filteredData.filter(item => item.status === 'Aktif').length,
    totalOverdue: filteredData.filter(item => item.status === 'Terlambat').length
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Reporting</h1>
          <p className="text-gray-600 mt-1">
            Generate dan unduh laporan aktivitas peminjaman
          </p>

          {/* Pre-filled indicator */}
          {(searchParams.get('dateFrom') || searchParams.get('dateTo')) && (
            <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-blue-800 font-medium">
                  Filter otomatis dari Calendar
                </span>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                Data telah difilter berdasarkan tanggal yang dipilih di halaman Calendar
              </p>
            </div>
          )}
        </div>

        {/* Filters */}
        <Card className="glass">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filter Laporan
            </h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Input
                label="Tanggal Mulai"
                type="date"
                value={filters.dateFrom}
                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              />
              <Input
                label="Tanggal Akhir"
                type="date"
                value={filters.dateTo}
                onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Kategori
                </label>
                <select
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                >
                  <option value="">Semua Kategori</option>
                  <option value="Elektronik">Elektronik</option>
                  <option value="Aksesoris">Aksesoris</option>
                  <option value="Furniture">Furniture</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <option value="">Semua Status</option>
                  <option value="Aktif">Aktif</option>
                  <option value="Dikembalikan">Dikembalikan</option>
                  <option value="Terlambat">Terlambat</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="glass">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{summary.totalBorrowings}</p>
                <p className="text-sm text-gray-600">Total Peminjaman</p>
              </div>
            </CardContent>
          </Card>
          <Card className="glass">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{summary.totalReturned}</p>
                <p className="text-sm text-gray-600">Dikembalikan</p>
              </div>
            </CardContent>
          </Card>
          <Card className="glass">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-yellow-600">{summary.totalActive}</p>
                <p className="text-sm text-gray-600">Aktif</p>
              </div>
            </CardContent>
          </Card>
          <Card className="glass">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">{summary.totalOverdue}</p>
                <p className="text-sm text-gray-600">Terlambat</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Export Message */}
        {exportMessage && (
          <div className={`p-4 rounded-lg border ${
            exportMessage.type === 'success'
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-red-50 border-red-200 text-red-800'
          }`}>
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">{exportMessage.message}</span>
            </div>
          </div>
        )}

        {/* Generate Reports */}
        <Card className="glass">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Download className="h-5 w-5 mr-2" />
              Generate Laporan
            </h3>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={() => handleGenerateReport('pdf')}
                loading={isGenerating}
                disabled={filteredData.length === 0}
                className="flex items-center space-x-2"
              >
                <FileText className="h-4 w-4" />
                <span>{isGenerating ? 'Generating PDF...' : 'Download PDF'}</span>
              </Button>
              <Button
                onClick={() => handleGenerateReport('excel')}
                loading={isGenerating}
                disabled={filteredData.length === 0}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <FileText className="h-4 w-4" />
                <span>{isGenerating ? 'Generating Excel...' : 'Download Excel'}</span>
              </Button>
            </div>
            <div className="mt-3 space-y-1">
              <p className="text-sm text-gray-500">
                Laporan akan mencakup data berdasarkan filter yang dipilih ({filteredData.length} record)
              </p>
              {filteredData.length === 0 && (
                <p className="text-sm text-red-600">
                  Tidak ada data untuk diekspor. Silakan sesuaikan filter.
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Preview Data */}
        <Card className="glass">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">
              Preview Data Laporan
            </h3>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Peminjam</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Barang</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Kategori</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Tanggal Pinjam</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Tanggal Kembali</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Tujuan</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredData.map((item) => (
                    <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4 text-gray-700">{item.borrowerName}</td>
                      <td className="py-3 px-4 text-gray-700">{item.itemName}</td>
                      <td className="py-3 px-4 text-gray-700">{item.category}</td>
                      <td className="py-3 px-4 text-gray-700">
                        {new Date(item.borrowDate).toLocaleDateString('id-ID')}
                      </td>
                      <td className="py-3 px-4 text-gray-700">
                        {item.returnDate
                          ? new Date(item.returnDate).toLocaleDateString('id-ID')
                          : '-'
                        }
                      </td>
                      <td className="py-3 px-4 text-gray-700">{item.purpose}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.status === 'Dikembalikan' ? 'text-green-600 bg-green-100' :
                          item.status === 'Aktif' ? 'text-blue-600 bg-blue-100' :
                          'text-red-600 bg-red-100'
                        }`}>
                          {item.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {filteredData.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  Tidak ada data yang sesuai dengan filter yang dipilih
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Report Templates */}
        <Card className="glass">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">
              Template Laporan
            </h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <h4 className="font-medium text-gray-900 mb-2">📊 Laporan Bulanan</h4>
                <p className="text-sm text-gray-600">
                  Ringkasan aktivitas peminjaman dalam satu bulan terakhir
                </p>
              </div>
              <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <h4 className="font-medium text-gray-900 mb-2">🔍 Laporan Detail</h4>
                <p className="text-sm text-gray-600">
                  Laporan lengkap dengan semua detail peminjaman dan pengembalian
                </p>
              </div>
              <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <h4 className="font-medium text-gray-900 mb-2">⚠️ Laporan Overdue</h4>
                <p className="text-sm text-gray-600">
                  Daftar peminjaman yang terlambat dikembalikan
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}

export default ReportsPage
