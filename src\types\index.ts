// Enum types matching Prisma schema
export enum ItemStatus {
  AVAILABLE = 'AVAILABLE',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  DISCONTINUED = 'DISCONTINUED'
}

export enum BorrowingStatus {
  ACTIVE = 'ACTIVE',
  RETURNED = 'RETURNED',
  OVERDUE = 'OVERDUE'
}

export enum ActivityType {
  ITEM_ADDED = 'ITEM_ADDED',
  ITEM_UPDATED = 'ITEM_UPDATED',
  ITEM_DELETED = 'ITEM_DELETED',
  ITEM_BORROWED = 'ITEM_BORROWED',
  ITEM_RETURNED = 'ITEM_RETURNED',
  STOCK_UPDATED = 'STOCK_UPDATED'
}

// Base types
export interface Category {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  items?: Item[];
}

export interface Location {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  items?: Item[];
}

export interface Item {
  id: string;
  name: string;
  description?: string;
  stock: number;
  minStock: number;
  status: ItemStatus;
  categoryId: string;
  locationId: string;
  createdAt: Date;
  updatedAt: Date;
  category?: Category;
  location?: Location;
  borrowings?: Borrowing[];
  activities?: Activity[];
}

export interface Borrowing {
  id: string;
  itemId: string;
  borrowerName: string;
  purpose: string;
  quantity: number;
  borrowDate: Date;
  returnDate?: Date;
  expectedReturnDate: Date;
  status: BorrowingStatus;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  item?: Item;
  activities?: Activity[];
}

export interface Activity {
  id: string;
  type: ActivityType;
  description: string;
  itemId?: string;
  borrowingId?: string;
  userId?: string;
  metadata?: any;
  createdAt: Date;
  item?: Item;
  borrowing?: Borrowing;
}

// Form types
export interface CreateItemForm {
  name: string;
  description?: string;
  stock: number;
  minStock: number;
  categoryId: string;
  locationId: string;
}

export interface UpdateItemForm extends Partial<CreateItemForm> {
  id: string;
  status?: ItemStatus;
}

export interface CreateBorrowingForm {
  itemId: string;
  borrowerName: string;
  purpose: string;
  quantity: number;
  expectedReturnDate: Date;
  notes?: string;
}

export interface CreateCategoryForm {
  name: string;
  description?: string;
}

export interface CreateLocationForm {
  name: string;
  description?: string;
}

// Dashboard KPI types
export interface DashboardKPI {
  totalItems: number;
  lowStockItems: number;
  totalBorrowedItems: number;
  overdueItems: number;
}

// Analytics types
export interface CategoryDistribution {
  categoryName: string;
  itemCount: number;
  percentage: number;
}

export interface MostBorrowedItem {
  itemName: string;
  borrowCount: number;
  categoryName: string;
}

export interface AnalyticsData {
  categoryDistribution: CategoryDistribution[];
  mostBorrowedItems: MostBorrowedItem[];
  monthlyBorrowingTrend: {
    month: string;
    borrowCount: number;
    returnCount: number;
  }[];
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Search and filter types
export interface ItemFilters {
  search?: string;
  categoryId?: string;
  locationId?: string;
  status?: ItemStatus;
  lowStock?: boolean;
}

export interface BorrowingFilters {
  search?: string;
  status?: BorrowingStatus;
  itemId?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

// Calendar types
export interface CalendarEvent {
  id: string;
  title: string;
  date: Date;
  type: ActivityType;
  description: string;
  metadata?: any;
}

// Report types
export interface ReportFilters {
  dateFrom: Date;
  dateTo: Date;
  categoryId?: string;
  locationId?: string;
  itemId?: string;
}

export interface BorrowingReport {
  borrowings: Borrowing[];
  summary: {
    totalBorrowings: number;
    totalReturned: number;
    totalActive: number;
    totalOverdue: number;
    mostBorrowedItems: MostBorrowedItem[];
  };
}

// UI Component types
export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: any, item: T) => React.ReactNode;
}

export interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

// Navigation types
export interface NavItem {
  label: string;
  href: string;
  icon: React.ComponentType<any>;
  active?: boolean;
}
