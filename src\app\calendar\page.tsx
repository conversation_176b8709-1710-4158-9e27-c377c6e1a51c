'use client'

import React, { useState } from 'react'
import AppLayout from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Modal from '@/components/ui/Modal'
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Plus, Package, ArrowRightLeft, RotateCcw } from 'lucide-react'
import { ActivityType } from '@/types'

// Mock data untuk calendar events
const mockEvents = [
  {
    id: '1',
    date: new Date('2025-01-28'),
    type: ActivityType.ITEM_BORROWED,
    title: 'Laptop Dell dipinjam',
    description: '<PERSON> meminjam Laptop Dell untuk presentasi klien',
    itemName: 'Laptop Dell Inspiron'
  },
  {
    id: '2',
    date: new Date('2025-01-28'),
    type: ActivityType.ITEM_RETURNED,
    title: 'Mouse Wireless dikembalikan',
    description: '<PERSON> mengembalikan Mouse Wireless',
    itemName: 'Mouse Wireless'
  },
  {
    id: '3',
    date: new Date('2025-01-27'),
    type: ActivityType.ITEM_ADDED,
    title: 'Keyboard baru ditambahkan',
    description: 'Keyboard Mechanical baru ditambahkan ke inventaris',
    itemName: 'Keyboard Mechanical'
  },
  {
    id: '4',
    date: new Date('2025-01-26'),
    type: ActivityType.STOCK_UPDATED,
    title: 'Stok diperbarui',
    description: 'Stok Kabel HDMI diperbarui menjadi 15 unit',
    itemName: 'Kabel HDMI'
  },
  {
    id: '5',
    date: new Date('2025-01-25'),
    type: ActivityType.ITEM_BORROWED,
    title: 'Proyektor dipinjam',
    description: 'Jane Smith meminjam Proyektor Epson untuk meeting',
    itemName: 'Proyektor Epson'
  }
]

const getActivityColor = (type: ActivityType) => {
  switch (type) {
    case ActivityType.ITEM_ADDED:
      return 'bg-green-500'
    case ActivityType.ITEM_BORROWED:
      return 'bg-blue-500'
    case ActivityType.ITEM_RETURNED:
      return 'bg-orange-500'
    case ActivityType.STOCK_UPDATED:
      return 'bg-purple-500'
    default:
      return 'bg-gray-500'
  }
}

const getActivityIcon = (type: ActivityType) => {
  switch (type) {
    case ActivityType.ITEM_ADDED:
      return <Plus className="h-3 w-3" />
    case ActivityType.ITEM_BORROWED:
      return <ArrowRightLeft className="h-3 w-3" />
    case ActivityType.ITEM_RETURNED:
      return <RotateCcw className="h-3 w-3" />
    case ActivityType.STOCK_UPDATED:
      return <Package className="h-3 w-3" />
    default:
      return <CalendarIcon className="h-3 w-3" />
  }
}

const CalendarPage: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const monthNames = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ]

  const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab']

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }
    
    return days
  }

  const getEventsForDate = (date: Date) => {
    return mockEvents.filter(event => 
      event.date.toDateString() === date.toDateString()
    )
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const handleDateClick = (date: Date) => {
    const events = getEventsForDate(date)
    if (events.length > 0) {
      setSelectedDate(date)
      setIsModalOpen(true)
    }
  }

  const selectedDateEvents = selectedDate ? getEventsForDate(selectedDate) : []
  const days = getDaysInMonth(currentDate)

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Kalender</h1>
            <p className="text-gray-600 mt-1">
              Lihat aktivitas inventaris berdasarkan tanggal
            </p>
          </div>
          <Button onClick={() => setCurrentDate(new Date())} variant="outline">
            Hari Ini
          </Button>
        </div>

        {/* Legend */}
        <Card className="glass">
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-500 rounded"></div>
                <span className="text-sm text-gray-600">Barang Ditambahkan</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-blue-500 rounded"></div>
                <span className="text-sm text-gray-600">Peminjaman</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-orange-500 rounded"></div>
                <span className="text-sm text-gray-600">Pengembalian</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-purple-500 rounded"></div>
                <span className="text-sm text-gray-600">Update Stok</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Calendar */}
        <Card className="glass">
          <CardHeader>
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">
                {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
              </h2>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateMonth('prev')}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateMonth('next')}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-7 gap-1">
              {/* Day headers */}
              {dayNames.map(day => (
                <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                  {day}
                </div>
              ))}
              
              {/* Calendar days */}
              {days.map((day, index) => {
                if (!day) {
                  return <div key={index} className="p-2 h-24"></div>
                }
                
                const events = getEventsForDate(day)
                const isToday = day.toDateString() === new Date().toDateString()
                const hasEvents = events.length > 0
                
                return (
                  <div
                    key={day.toISOString()}
                    className={`p-2 h-24 border border-gray-100 cursor-pointer transition-colors ${
                      isToday ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                    } ${hasEvents ? 'cursor-pointer' : 'cursor-default'}`}
                    onClick={() => hasEvents && handleDateClick(day)}
                  >
                    <div className={`text-sm font-medium mb-1 ${
                      isToday ? 'text-blue-600' : 'text-gray-900'
                    }`}>
                      {day.getDate()}
                    </div>
                    
                    <div className="space-y-1">
                      {events.slice(0, 2).map(event => (
                        <div
                          key={event.id}
                          className={`text-xs px-1 py-0.5 rounded text-white flex items-center space-x-1 ${getActivityColor(event.type)}`}
                        >
                          {getActivityIcon(event.type)}
                          <span className="truncate">{event.title}</span>
                        </div>
                      ))}
                      
                      {events.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{events.length - 2} lainnya
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Event Details Modal */}
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title={selectedDate ? `Aktivitas - ${selectedDate.toLocaleDateString('id-ID', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}` : ''}
          size="md"
        >
          <div className="space-y-4">
            {selectedDateEvents.map(event => (
              <div key={event.id} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-full text-white ${getActivityColor(event.type)}`}>
                    {getActivityIcon(event.type)}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{event.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-xs text-gray-500">Item:</span>
                      <span className="text-xs font-medium text-gray-700">{event.itemName}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {selectedDateEvents.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                Tidak ada aktivitas pada tanggal ini
              </div>
            )}
          </div>
        </Modal>

        {/* Recent Activities */}
        <Card className="glass">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">
              Aktivitas Terbaru
            </h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockEvents.slice(0, 5).map(event => (
                <div key={event.id} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg">
                  <div className={`p-2 rounded-full text-white ${getActivityColor(event.type)}`}>
                    {getActivityIcon(event.type)}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{event.title}</div>
                    <div className="text-sm text-gray-600">{event.description}</div>
                  </div>
                  <div className="text-sm text-gray-500">
                    {event.date.toLocaleDateString('id-ID', { month: 'short', day: 'numeric' })}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}

export default CalendarPage
