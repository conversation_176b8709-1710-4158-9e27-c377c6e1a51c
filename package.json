{"name": "disatools", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.12.0", "chart.js": "^4.5.0", "clsx": "^2.1.1", "jspdf": "^3.0.1", "lucide-react": "^0.527.0", "mysql2": "^3.14.2", "next": "15.4.4", "prisma": "^6.12.0", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "19.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}