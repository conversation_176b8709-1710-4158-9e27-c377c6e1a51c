'use client'

import React, { useState } from 'react'
import AppLayout from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Modal from '@/components/ui/Modal'
import EnhancedBorrowingForm from '@/components/borrowing/EnhancedBorrowingForm'
import { Plus, Search, ArrowLeft, Clock, CheckCircle, Package } from 'lucide-react'
import { BorrowingStatus } from '@/types'

// Mock data untuk sementara - Updated structure for multi-item borrowing
const mockBorrowings = [
  {
    id: '1',
    borrowerName: '<PERSON>',
    purpose: 'Presentasi klien',
    borrowDate: new Date('2025-01-25'),
    expectedReturnDate: new Date('2025-01-30'),
    returnDate: null,
    status: BorrowingStatus.ACTIVE,
    notes: 'Untuk presentasi di kantor cabang',
    items: [
      {
        id: '1-1',
        item: {
          id: '1',
          name: 'Laptop Dell Inspiron',
          category: { name: 'Elektronik' }
        },
        quantity: 1,
        returnedQuantity: 0,
        status: BorrowingStatus.ACTIVE
      }
    ]
  },
  {
    id: '2',
    borrowerName: 'Jane Smith',
    purpose: 'Meeting tim',
    borrowDate: new Date('2025-01-20'),
    expectedReturnDate: new Date('2025-01-27'),
    returnDate: null,
    status: BorrowingStatus.OVERDUE,
    notes: '',
    items: [
      {
        id: '2-1',
        item: {
          id: '2',
          name: 'Proyektor Epson',
          category: { name: 'Elektronik' }
        },
        quantity: 1,
        returnedQuantity: 0,
        status: BorrowingStatus.OVERDUE
      },
      {
        id: '2-2',
        item: {
          id: '3',
          name: 'Kabel HDMI',
          category: { name: 'Aksesoris' }
        },
        quantity: 2,
        returnedQuantity: 0,
        status: BorrowingStatus.OVERDUE
      }
    ]
  },
  {
    id: '3',
    borrowerName: 'Bob Johnson',
    purpose: 'Work from home setup',
    borrowDate: new Date('2025-01-22'),
    expectedReturnDate: new Date('2025-01-29'),
    returnDate: new Date('2025-01-28'),
    status: BorrowingStatus.RETURNED,
    notes: 'Dikembalikan dalam kondisi baik',
    items: [
      {
        id: '3-1',
        item: {
          id: '4',
          name: 'Mouse Wireless',
          category: { name: 'Aksesoris' }
        },
        quantity: 2,
        returnedQuantity: 2,
        status: BorrowingStatus.RETURNED
      },
      {
        id: '3-2',
        item: {
          id: '5',
          name: 'Keyboard Mechanical',
          category: { name: 'Aksesoris' }
        },
        quantity: 1,
        returnedQuantity: 1,
        status: BorrowingStatus.RETURNED
      }
    ]
  }
]

const mockAvailableItems = [
  { id: '1', name: 'Laptop Dell Inspiron', stock: 5 },
  { id: '2', name: 'Mouse Wireless', stock: 8 },
  { id: '3', name: 'Keyboard Mechanical', stock: 3 },
  { id: '4', name: 'Monitor 24 inch', stock: 2 }
]

const getStatusColor = (status: BorrowingStatus) => {
  switch (status) {
    case BorrowingStatus.ACTIVE:
      return 'text-blue-600 bg-blue-100'
    case BorrowingStatus.RETURNED:
      return 'text-green-600 bg-green-100'
    case BorrowingStatus.OVERDUE:
      return 'text-red-600 bg-red-100'
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

const isOverdue = (expectedReturnDate: Date, status: BorrowingStatus) => {
  return status === BorrowingStatus.ACTIVE && new Date() > expectedReturnDate
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const BorrowingPage: React.FC = () => {
  const [borrowings, setBorrowings] = useState(mockBorrowings)
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [statusFilter, setStatusFilter] = useState<BorrowingStatus | 'ALL'>('ALL')
  const [isLoading, setIsLoading] = useState(false)

  const filteredBorrowings = borrowings.filter(borrowing => {
    const matchesSearch =
      borrowing.borrowerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      borrowing.items?.some(item =>
        item.item.name.toLowerCase().includes(searchTerm.toLowerCase())
      ) ||
      borrowing.purpose.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'ALL' || borrowing.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const handleAddBorrowing = () => {
    setIsModalOpen(true)
  }

  const handleReturnItem = (borrowingId: string) => {
    if (confirm('Konfirmasi pengembalian item ini?')) {
      setBorrowings(borrowings.map(borrowing =>
        borrowing.id === borrowingId
          ? {
              ...borrowing,
              status: BorrowingStatus.RETURNED,
              returnDate: new Date()
            }
          : borrowing
      ))
    }
  }

  const handleSubmit = async (formData: any) => {
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Create new borrowing with multi-item structure
      const newBorrowing = {
        id: Date.now().toString(),
        borrowerName: formData.borrowerName,
        purpose: formData.purpose,
        borrowDate: new Date(),
        expectedReturnDate: new Date(formData.expectedReturnDate),
        returnDate: null,
        status: BorrowingStatus.ACTIVE,
        notes: formData.notes,
        items: formData.items.map((item: any, index: number) => {
          const selectedItem = mockAvailableItems.find(ai => ai.id === item.itemId)
          return {
            id: `${Date.now()}-${index}`,
            item: {
              id: selectedItem?.id || '',
              name: selectedItem?.name || '',
              category: selectedItem?.category || { name: 'Unknown' }
            },
            quantity: item.quantity,
            returnedQuantity: 0,
            status: BorrowingStatus.ACTIVE,
            notes: item.notes
          }
        })
      }

      setBorrowings([newBorrowing, ...borrowings])
      setIsModalOpen(false)
    } catch (error) {
      console.error('Error creating borrowing:', error)
      alert('Gagal membuat peminjaman. Silakan coba lagi.')
    } finally {
      setIsLoading(false)
    }
  }

  const activeBorrowings = borrowings.filter(b => b.status === BorrowingStatus.ACTIVE).length
  const overdueBorrowings = borrowings.filter(b => b.status === BorrowingStatus.OVERDUE).length

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Peminjaman</h1>
            <p className="text-gray-600 mt-1">
              Kelola peminjaman barang inventaris
            </p>
          </div>
          <Button onClick={handleAddBorrowing} className="flex items-center space-x-2">
            <Plus className="h-4 w-4" />
            <span>Pinjam Barang</span>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Sedang Dipinjam</p>
                  <p className="text-3xl font-bold text-blue-600">{activeBorrowings}</p>
                </div>
                <div className="p-3 rounded-full bg-blue-100">
                  <ArrowLeft className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Terlambat</p>
                  <p className="text-3xl font-bold text-red-600">{overdueBorrowings}</p>
                </div>
                <div className="p-3 rounded-full bg-red-100">
                  <Clock className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Peminjaman</p>
                  <p className="text-3xl font-bold text-green-600">{borrowings.length}</p>
                </div>
                <div className="p-3 rounded-full bg-green-100">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="glass">
          <CardContent className="p-4">
            <div className="flex space-x-4">
              <div className="flex-1">
                <Input
                  placeholder="Cari peminjam, barang, atau tujuan..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  leftIcon={<Search className="h-4 w-4" />}
                />
              </div>
              <div>
                <select
                  className="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as BorrowingStatus | 'ALL')}
                >
                  <option value="ALL">Semua Status</option>
                  <option value={BorrowingStatus.ACTIVE}>Aktif</option>
                  <option value={BorrowingStatus.OVERDUE}>Terlambat</option>
                  <option value={BorrowingStatus.RETURNED}>Dikembalikan</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Borrowings Table */}
        <Card className="glass">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">
              Daftar Peminjaman ({filteredBorrowings.length})
            </h3>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Peminjam</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Barang</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Tujuan</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Tanggal Pinjam</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Tanggal Kembali</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredBorrowings.map((borrowing) => (
                    <tr key={borrowing.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{borrowing.borrowerName}</div>
                        <div className="text-sm text-gray-500">
                          {formatDate(borrowing.borrowDate)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="space-y-1">
                          {borrowing.items?.map((item, index) => (
                            <div key={item.id} className="flex items-center space-x-2">
                              <Package className="h-3 w-3 text-gray-400" />
                              <span className="text-sm font-medium text-gray-900">
                                {item.item.name}
                              </span>
                              <span className="text-xs text-gray-500">
                                ({item.quantity} unit)
                              </span>
                              {item.returnedQuantity > 0 && (
                                <span className="text-xs text-green-600">
                                  {item.returnedQuantity} dikembalikan
                                </span>
                              )}
                            </div>
                          ))}
                          <div className="text-xs text-gray-500">
                            Total: {borrowing.items?.reduce((sum, item) => sum + item.quantity, 0)} unit
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-700">{borrowing.purpose}</div>
                        {borrowing.notes && (
                          <div className="text-xs text-gray-500 mt-1">{borrowing.notes}</div>
                        )}
                      </td>
                      <td className="py-3 px-4 text-gray-700">
                        {formatDate(borrowing.borrowDate)}
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-700">{formatDate(borrowing.expectedReturnDate)}</div>
                        {isOverdue(borrowing.expectedReturnDate, borrowing.status) && (
                          <div className="text-xs text-red-600">Terlambat</div>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(borrowing.status)}`}>
                          {borrowing.status === BorrowingStatus.ACTIVE ? 'Aktif' :
                           borrowing.status === BorrowingStatus.RETURNED ? 'Dikembalikan' : 'Terlambat'}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        {borrowing.status === BorrowingStatus.ACTIVE && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleReturnItem(borrowing.id)}
                            className="text-green-600 border-green-600 hover:bg-green-50"
                          >
                            Kembalikan
                          </Button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Borrowing Modal */}
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title="Pinjam Barang Baru"
          size="xl"
        >
          <EnhancedBorrowingForm
            onSubmit={handleSubmit}
            onCancel={() => setIsModalOpen(false)}
            availableItems={mockAvailableItems}
            isLoading={isLoading}
          />
        </Modal>
      </div>
    </AppLayout>
  )
}

export default BorrowingPage
