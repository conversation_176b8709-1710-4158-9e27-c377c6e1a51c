import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { BorrowingStatus } from '@/types'

// GET /api/borrowings - Get all borrowings with optional filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const status = searchParams.get('status') as BorrowingStatus
    const itemId = searchParams.get('itemId')
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    const where: any = {}

    if (search) {
      where.OR = [
        { borrowerName: { contains: search, mode: 'insensitive' } },
        { purpose: { contains: search, mode: 'insensitive' } },
        { item: { name: { contains: search, mode: 'insensitive' } } }
      ]
    }

    if (status) {
      where.status = status
    }

    if (itemId) {
      where.itemId = itemId
    }

    if (dateFrom) {
      where.borrowDate = { gte: new Date(dateFrom) }
    }

    if (dateTo) {
      if (where.borrowDate) {
        where.borrowDate.lte = new Date(dateTo)
      } else {
        where.borrowDate = { lte: new Date(dateTo) }
      }
    }

    const [borrowings, total] = await Promise.all([
      prisma.borrowing.findMany({
        where,
        include: {
          item: {
            include: {
              category: true,
              location: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.borrowing.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: borrowings,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching borrowings:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch borrowings' },
      { status: 500 }
    )
  }
}

// POST /api/borrowings - Create new borrowing
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { itemId, borrowerName, purpose, quantity, expectedReturnDate, notes } = body

    // Validate required fields
    if (!itemId || !borrowerName || !purpose || !quantity || !expectedReturnDate) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check if item exists and has sufficient stock
    const item = await prisma.item.findUnique({
      where: { id: itemId },
      include: { category: true }
    })

    if (!item) {
      return NextResponse.json(
        { success: false, error: 'Item not found' },
        { status: 404 }
      )
    }

    if (item.stock < parseInt(quantity)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient stock' },
        { status: 400 }
      )
    }

    // Create borrowing and update item stock in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create borrowing
      const borrowing = await tx.borrowing.create({
        data: {
          itemId,
          borrowerName,
          purpose,
          quantity: parseInt(quantity),
          expectedReturnDate: new Date(expectedReturnDate),
          notes,
          status: BorrowingStatus.ACTIVE
        },
        include: {
          item: {
            include: {
              category: true,
              location: true
            }
          }
        }
      })

      // Update item stock
      const updatedItem = await tx.item.update({
        where: { id: itemId },
        data: {
          stock: { decrement: parseInt(quantity) },
          status: item.stock - parseInt(quantity) === 0 ? 'OUT_OF_STOCK' : item.status
        }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'ITEM_BORROWED',
          description: `${quantity} unit "${item.name}" dipinjam oleh ${borrowerName}`,
          itemId,
          borrowingId: borrowing.id,
          metadata: {
            borrowerName,
            purpose,
            quantity: parseInt(quantity),
            expectedReturnDate,
            previousStock: item.stock,
            newStock: updatedItem.stock
          }
        }
      })

      return borrowing
    })

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Borrowing created successfully'
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating borrowing:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create borrowing' },
      { status: 500 }
    )
  }
}
