'use client'

import React from 'react'
import AppLayout from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { BarChart3, PieChart, TrendingUp, Package } from 'lucide-react'

// Mock data untuk analytics
const mockCategoryDistribution = [
  { categoryName: 'Elektronik', itemCount: 45, percentage: 35 },
  { categoryName: 'Aksesoris', itemCount: 32, percentage: 25 },
  { categoryName: 'Furniture', itemCount: 28, percentage: 22 },
  { categoryName: 'Alat Tulis', itemCount: 23, percentage: 18 }
]

const mockMostBorrowedItems = [
  { itemName: 'Laptop Dell', borrowCount: 15, categoryName: 'Elektronik' },
  { itemName: 'Proyektor Epson', borrowCount: 12, categoryName: 'Elektronik' },
  { itemName: 'Mouse Wireless', borrowCount: 10, categoryName: 'Aksesoris' },
  { itemName: 'Keyboard Mechanical', borrowCount: 8, categoryName: 'Aksesoris' },
  { itemName: 'Monitor 24"', borrowCount: 7, categoryName: 'Elektronik' }
]

const mockMonthlyTrend = [
  { month: 'Jan', borrowCount: 25, returnCount: 23 },
  { month: 'Feb', borrowCount: 32, returnCount: 28 },
  { month: 'Mar', borrowCount: 28, returnCount: 30 },
  { month: 'Apr', borrowCount: 35, returnCount: 32 },
  { month: 'May', borrowCount: 42, returnCount: 38 },
  { month: 'Jun', borrowCount: 38, returnCount: 40 }
]

const CategoryChart: React.FC = () => {
  const maxCount = Math.max(...mockCategoryDistribution.map(item => item.itemCount))
  
  return (
    <div className="space-y-4">
      {mockCategoryDistribution.map((item, index) => (
        <div key={item.categoryName} className="flex items-center space-x-4">
          <div className="w-24 text-sm font-medium text-gray-700">
            {item.categoryName}
          </div>
          <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
            <div
              className="bg-blue-500 h-6 rounded-full transition-all duration-500 flex items-center justify-end pr-2"
              style={{ width: `${(item.itemCount / maxCount) * 100}%` }}
            >
              <span className="text-white text-xs font-medium">
                {item.itemCount}
              </span>
            </div>
          </div>
          <div className="w-12 text-sm text-gray-600">
            {item.percentage}%
          </div>
        </div>
      ))}
    </div>
  )
}

const MostBorrowedChart: React.FC = () => {
  const maxCount = Math.max(...mockMostBorrowedItems.map(item => item.borrowCount))
  
  return (
    <div className="space-y-4">
      {mockMostBorrowedItems.map((item, index) => (
        <div key={item.itemName} className="flex items-center space-x-4">
          <div className="w-8 text-center">
            <span className="text-sm font-bold text-gray-500">#{index + 1}</span>
          </div>
          <div className="w-32 text-sm font-medium text-gray-700">
            {item.itemName}
          </div>
          <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
            <div
              className="bg-green-500 h-6 rounded-full transition-all duration-500 flex items-center justify-end pr-2"
              style={{ width: `${(item.borrowCount / maxCount) * 100}%` }}
            >
              <span className="text-white text-xs font-medium">
                {item.borrowCount}x
              </span>
            </div>
          </div>
          <div className="w-20 text-xs text-gray-500">
            {item.categoryName}
          </div>
        </div>
      ))}
    </div>
  )
}

const TrendChart: React.FC = () => {
  const maxValue = Math.max(
    ...mockMonthlyTrend.map(item => Math.max(item.borrowCount, item.returnCount))
  )
  
  return (
    <div className="space-y-6">
      <div className="flex justify-center space-x-6">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-blue-500 rounded"></div>
          <span className="text-sm text-gray-600">Peminjaman</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-green-500 rounded"></div>
          <span className="text-sm text-gray-600">Pengembalian</span>
        </div>
      </div>
      
      <div className="flex items-end justify-between h-48 space-x-2">
        {mockMonthlyTrend.map((item) => (
          <div key={item.month} className="flex flex-col items-center space-y-2 flex-1">
            <div className="flex items-end space-x-1 h-40">
              <div
                className="bg-blue-500 rounded-t transition-all duration-500 w-6"
                style={{ height: `${(item.borrowCount / maxValue) * 100}%` }}
              />
              <div
                className="bg-green-500 rounded-t transition-all duration-500 w-6"
                style={{ height: `${(item.returnCount / maxValue) * 100}%` }}
              />
            </div>
            <div className="text-xs text-gray-600 font-medium">
              {item.month}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

const AnalyticsPage: React.FC = () => {
  const totalItems = mockCategoryDistribution.reduce((sum, item) => sum + item.itemCount, 0)
  const totalBorrowings = mockMostBorrowedItems.reduce((sum, item) => sum + item.borrowCount, 0)
  const avgBorrowingPerMonth = Math.round(
    mockMonthlyTrend.reduce((sum, item) => sum + item.borrowCount, 0) / mockMonthlyTrend.length
  )

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analitik</h1>
          <p className="text-gray-600 mt-1">
            Visualisasi data dan insight inventaris
          </p>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Item</p>
                  <p className="text-2xl font-bold text-blue-600">{totalItems}</p>
                </div>
                <Package className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Peminjaman</p>
                  <p className="text-2xl font-bold text-green-600">{totalBorrowings}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Rata-rata/Bulan</p>
                  <p className="text-2xl font-bold text-purple-600">{avgBorrowingPerMonth}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Kategori</p>
                  <p className="text-2xl font-bold text-orange-600">{mockCategoryDistribution.length}</p>
                </div>
                <PieChart className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Category Distribution */}
          <Card className="glass">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <PieChart className="h-5 w-5 mr-2 text-blue-600" />
                Distribusi Kategori
              </h3>
              <p className="text-sm text-gray-600">
                Jumlah barang per kategori
              </p>
            </CardHeader>
            <CardContent>
              <CategoryChart />
            </CardContent>
          </Card>

          {/* Most Borrowed Items */}
          <Card className="glass">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-green-600" />
                Barang Paling Sering Dipinjam
              </h3>
              <p className="text-sm text-gray-600">
                Top 5 item dengan peminjaman terbanyak
              </p>
            </CardHeader>
            <CardContent>
              <MostBorrowedChart />
            </CardContent>
          </Card>
        </div>

        {/* Monthly Trend */}
        <Card className="glass">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-purple-600" />
              Trend Peminjaman Bulanan
            </h3>
            <p className="text-sm text-gray-600">
              Perbandingan peminjaman dan pengembalian per bulan
            </p>
          </CardHeader>
          <CardContent>
            <TrendChart />
          </CardContent>
        </Card>

        {/* Insights */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="glass border-l-4 border-l-blue-500">
            <CardContent className="p-6">
              <h4 className="font-semibold text-gray-900 mb-2">📊 Kategori Terpopuler</h4>
              <p className="text-sm text-gray-600">
                Elektronik mendominasi dengan 35% dari total inventaris, menunjukkan kebutuhan tinggi akan perangkat teknologi.
              </p>
            </CardContent>
          </Card>

          <Card className="glass border-l-4 border-l-green-500">
            <CardContent className="p-6">
              <h4 className="font-semibold text-gray-900 mb-2">🔥 Item Favorit</h4>
              <p className="text-sm text-gray-600">
                Laptop Dell menjadi item paling sering dipinjam dengan 15 kali peminjaman, ideal untuk investasi lebih lanjut.
              </p>
            </CardContent>
          </Card>

          <Card className="glass border-l-4 border-l-purple-500">
            <CardContent className="p-6">
              <h4 className="font-semibold text-gray-900 mb-2">📈 Trend Positif</h4>
              <p className="text-sm text-gray-600">
                Tingkat pengembalian mencapai 90%, menunjukkan kedisiplinan yang baik dalam pengelolaan inventaris.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  )
}

export default AnalyticsPage
