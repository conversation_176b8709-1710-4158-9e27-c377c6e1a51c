import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { BorrowingStatus } from '@/types'

// POST /api/borrowings/[id]/return - Return borrowed item
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { notes } = body

    // Check if borrowing exists and is active
    const borrowing = await prisma.borrowing.findUnique({
      where: { id: params.id },
      include: {
        item: {
          include: {
            category: true,
            location: true
          }
        }
      }
    })

    if (!borrowing) {
      return NextResponse.json(
        { success: false, error: 'Borrowing not found' },
        { status: 404 }
      )
    }

    if (borrowing.status !== BorrowingStatus.ACTIVE) {
      return NextResponse.json(
        { success: false, error: 'Borrowing is not active' },
        { status: 400 }
      )
    }

    // Return item and update stock in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update borrowing status
      const updatedBorrowing = await tx.borrowing.update({
        where: { id: params.id },
        data: {
          status: BorrowingStatus.RETURNED,
          returnDate: new Date(),
          notes: notes || borrowing.notes
        },
        include: {
          item: {
            include: {
              category: true,
              location: true
            }
          }
        }
      })

      // Update item stock
      const updatedItem = await tx.item.update({
        where: { id: borrowing.itemId },
        data: {
          stock: { increment: borrowing.quantity },
          status: 'AVAILABLE' // Item becomes available when returned
        }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'ITEM_RETURNED',
          description: `${borrowing.quantity} unit "${borrowing.item.name}" dikembalikan oleh ${borrowing.borrowerName}`,
          itemId: borrowing.itemId,
          borrowingId: borrowing.id,
          metadata: {
            borrowerName: borrowing.borrowerName,
            quantity: borrowing.quantity,
            borrowDate: borrowing.borrowDate,
            returnDate: new Date(),
            previousStock: borrowing.item.stock,
            newStock: updatedItem.stock,
            isOverdue: new Date() > borrowing.expectedReturnDate
          }
        }
      })

      return updatedBorrowing
    })

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Item returned successfully'
    })
  } catch (error) {
    console.error('Error returning item:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to return item' },
      { status: 500 }
    )
  }
}
